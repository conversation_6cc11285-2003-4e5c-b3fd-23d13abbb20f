import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { StyleSheet } from 'react-native';
import { SafeAreaProvider } from "react-native-safe-area-context"
import { Toaster } from 'sonner-native';
import HomeScreen from "./screens/HomeScreen"
import BookRoomScreen from "./screens/BookRoomScreen"
import ParkingScreen from "./screens/ParkingScreen"
import AttendanceScreen from "./screens/AttendanceScreen"
import AdminDashboardScreen from "./screens/AdminDashboardScreen"
import ChatbotScreen from "./screens/ChatbotScreen"

// Define the type for our navigation stack parameters
export type RootStackParamList = {
  Home: undefined;
  BookRoom: undefined;
  Parking: undefined;
  Attendance: undefined;
  AdminDashboard: undefined;
  Chatbot: undefined;
};

const Stack = createNativeStackNavigator<RootStackParamList>();

function RootStack() {
  return (
    <Stack.Navigator screenOptions={{
      headerShown: false,
      animation: 'slide_from_right',
      contentStyle: { backgroundColor: '#FFFFFF' }
    }}>
      <Stack.Screen name="Home" component={HomeScreen} />
      <Stack.Screen name="BookRoom" component={BookRoomScreen} />
      <Stack.Screen name="Parking" component={ParkingScreen} />
      <Stack.Screen name="Attendance" component={AttendanceScreen} />
      <Stack.Screen name="AdminDashboard" component={AdminDashboardScreen} />
      <Stack.Screen name="Chatbot" component={ChatbotScreen} />
    </Stack.Navigator>
  );
}

export default function App() {
  return (
    <SafeAreaProvider style={styles.container}>
      <Toaster richColors closeButton />
      <NavigationContainer>
        <RootStack />
      </NavigationContainer>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    userSelect: "none"
  }
});